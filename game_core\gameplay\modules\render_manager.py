"""
RenderManager - <PERSON>les drawing and rendering

This module is responsible for:
- Map layer rendering
- Entity rendering (player, enemies)
- UI rendering (HUD, inventories)
- Effect rendering (animations, particles)
- Depth-based rendering order
"""
import pygame


class RenderManager:
    """Handles drawing and rendering operations"""
    
    def __init__(self, play_screen):
        """Initialize the RenderManager with reference to PlayScreen"""
        self.play_screen = play_screen
    
    def draw(self, surface):
        """Main draw method that renders everything"""
        # Clear the surface
        surface.fill((0, 0, 0))  # Black background
        
        # Draw map and entities
        self._draw_map_and_entities(surface)
        
        # Draw UI elements
        self._draw_ui_elements(surface)
        
        # Draw overlays
        self._draw_overlays(surface)
    
    def _draw_map_and_entities(self, surface):
        """Draw map layers and entities with proper depth ordering"""
        # Draw map tiles with depth - first two layers, then player, then remaining layers
        if hasattr(self.play_screen, 'layers') and self.play_screen.layers:
            self._draw_layered_map_with_entities(surface)
        else:
            self._draw_simple_map_with_entities(surface)
    
    def _draw_layered_map_with_entities(self, surface):
        """Draw layered map with entities at proper depth"""
        # Draw first layer (layer 0)
        if len(self.play_screen.layers) > 0 and self.play_screen.layers[0]["visible"]:
            self._draw_single_map_layer(surface, 0)
            
            # Draw key item collection animations for first layer
            self.play_screen.key_item_manager.draw_layer(
                surface, 
                self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
                self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
                self.play_screen.grid_cell_size, 0
            )
            
            # Draw crystal item collection animations for first layer
            self.play_screen.crystal_item_manager.draw_layer(
                surface,
                self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
                self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
                self.play_screen.grid_cell_size, 0
            )

        # Draw second layer (layer 1) if it exists
        if len(self.play_screen.layers) > 1 and self.play_screen.layers[1]["visible"]:
            self._draw_single_map_layer(surface, 1)
            
            # Draw key item collection animations for second layer
            self.play_screen.key_item_manager.draw_layer(
                surface,
                self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
                self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
                self.play_screen.grid_cell_size, 1
            )
            
            # Draw crystal item collection animations for second layer
            self.play_screen.crystal_item_manager.draw_layer(
                surface,
                self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
                self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
                self.play_screen.grid_cell_size, 1
            )

        # Draw enemies
        self.play_screen.enemy_manager.draw(
            surface,
            self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
            self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
            self.play_screen.camera_controller.zoom_factor
        )

        # Draw player character after second layer but before higher layers
        if self.play_screen.player:
            self.play_screen.player.draw(
                surface,
                self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
                self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
                self.play_screen.camera_controller.zoom_factor
            )

        # Draw relation points
        self.play_screen.relation_handler.draw(
            surface,
            self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
            self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
            self.play_screen.grid_cell_size
        )

        # Draw remaining layers (2 to max) on top of player
        for layer_idx in range(2, len(self.play_screen.layers)):
            layer = self.play_screen.layers[layer_idx]
            if layer["visible"]:
                # Draw the layer
                self._draw_single_map_layer(surface, layer_idx)
                
                # Draw key item collection animations for this layer
                self.play_screen.key_item_manager.draw_layer(
                    surface,
                    self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
                    self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
                    self.play_screen.grid_cell_size, layer_idx
                )
                
                # Draw crystal item collection animations for this layer
                self.play_screen.crystal_item_manager.draw_layer(
                    surface,
                    self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
                    self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
                    self.play_screen.grid_cell_size, layer_idx
                )
    
    def _draw_simple_map_with_entities(self, surface):
        """Draw simple map format with entities"""
        # Draw the map
        self._draw_map_layers(surface, 0, 0)  # Single layer
        
        # Draw enemies first
        self.play_screen.enemy_manager.draw(
            surface,
            self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
            self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
            self.play_screen.camera_controller.zoom_factor
        )

        # Draw player character on top of enemies if it exists
        if self.play_screen.player:
            self.play_screen.player.draw(
                surface,
                self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
                self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
                self.play_screen.camera_controller.zoom_factor
            )

        # Draw relation points
        self.play_screen.relation_handler.draw(
            surface,
            self.play_screen.camera_controller.camera_x - self.play_screen.camera_controller.center_offset_x,
            self.play_screen.camera_controller.camera_y - self.play_screen.camera_controller.center_offset_y,
            self.play_screen.grid_cell_size
        )
    
    def _draw_single_map_layer(self, surface, layer_idx):
        """Draw a single map layer"""
        if not hasattr(self.play_screen, 'layers') or layer_idx >= len(self.play_screen.layers):
            return
            
        layer = self.play_screen.layers[layer_idx]
        if not layer["visible"]:
            return
            
        layer_data = layer["data"]
        
        # Calculate visible tile range based on camera and zoom
        camera_x = self.play_screen.camera_controller.camera_x
        camera_y = self.play_screen.camera_controller.camera_y
        center_offset_x = self.play_screen.camera_controller.center_offset_x
        center_offset_y = self.play_screen.camera_controller.center_offset_y
        zoom_factor = self.play_screen.camera_controller.zoom_factor
        
        # Calculate which tiles are visible
        start_x = max(0, int((camera_x - center_offset_x) // self.play_screen.grid_cell_size))
        start_y = max(0, int((camera_y - center_offset_y) // self.play_screen.grid_cell_size))
        end_x = min(self.play_screen.map_width, 
                   int((camera_x - center_offset_x + self.play_screen.width / zoom_factor) // self.play_screen.grid_cell_size) + 1)
        end_y = min(self.play_screen.map_height,
                   int((camera_y - center_offset_y + self.play_screen.height / zoom_factor) // self.play_screen.grid_cell_size) + 1)
        
        # Draw visible tiles
        for y in range(start_y, end_y):
            if y >= len(layer_data):
                continue
            row = layer_data[y]
            for x in range(start_x, end_x):
                if x >= len(row):
                    continue
                tile_id = row[x]
                if tile_id != -1:  # -1 means no tile
                    self._draw_tile(surface, tile_id, x, y, camera_x, camera_y, center_offset_x, center_offset_y, zoom_factor)
    
    def _draw_tile(self, surface, tile_id, grid_x, grid_y, camera_x, camera_y, center_offset_x, center_offset_y, zoom_factor):
        """Draw a single tile"""
        # Calculate screen position
        world_x = grid_x * self.play_screen.grid_cell_size
        world_y = grid_y * self.play_screen.grid_cell_size
        screen_x = (world_x - camera_x + center_offset_x) * zoom_factor
        screen_y = (world_y - camera_y + center_offset_y) * zoom_factor
        
        # Get tile image
        if tile_id in self.play_screen.tiles:
            tile_image = self.play_screen.tiles[tile_id]
            # Scale tile if zoomed
            if zoom_factor != 1.0:
                scaled_size = int(self.play_screen.grid_cell_size * zoom_factor)
                tile_image = pygame.transform.scale(tile_image, (scaled_size, scaled_size))
            surface.blit(tile_image, (screen_x, screen_y))
        elif str(tile_id) in self.play_screen.expanded_mapping:
            # Handle animated tiles
            tile_info = self.play_screen.expanded_mapping[str(tile_id)]
            if tile_info["path"].startswith("animated:"):
                tile_name = tile_info["path"][9:]  # Remove "animated:" prefix
                animated_tile = self.play_screen.animated_tile_manager.get_current_frame(tile_name)
                if animated_tile:
                    # Scale tile if zoomed
                    if zoom_factor != 1.0:
                        scaled_size = int(self.play_screen.grid_cell_size * zoom_factor)
                        animated_tile = pygame.transform.scale(animated_tile, (scaled_size, scaled_size))
                    surface.blit(animated_tile, (screen_x, screen_y))
    
    def _draw_map_layers(self, surface, start_layer, end_layer):
        """Draw multiple map layers"""
        for layer_idx in range(start_layer, end_layer + 1):
            self._draw_single_map_layer(surface, layer_idx)
    
    def _draw_ui_elements(self, surface):
        """Draw UI elements"""
        # Draw HUD
        if hasattr(self.play_screen, 'hud') and self.play_screen.hud:
            self.play_screen.hud.draw(surface)
        
        # Draw chest inventory if visible
        if (hasattr(self.play_screen, 'chest_inventory') and 
            self.play_screen.chest_inventory.is_visible()):
            self.play_screen.chest_inventory.draw(surface)
        
        # Draw player inventory if visible
        if (hasattr(self.play_screen, 'player_inventory') and 
            self.play_screen.player_inventory.is_visible()):
            self.play_screen.player_inventory.draw(surface)
    
    def _draw_overlays(self, surface):
        """Draw overlays and special screens"""
        # Draw game over screen if showing
        if self.play_screen.show_game_over:
            self.play_screen.game_over_screen.draw(surface)
        
        # Draw status message if any
        if self.play_screen.status_timer > 0:
            self._draw_status_message(surface)
        
        # Draw popup message if any
        if self.play_screen.popup_timer > 0:
            self._draw_popup_message(surface)
    
    def _draw_status_message(self, surface):
        """Draw status message"""
        if hasattr(self.play_screen, 'font'):
            text_surface = self.play_screen.font.render(self.play_screen.status_message, True, (255, 255, 255))
            surface.blit(text_surface, (10, 10))
    
    def _draw_popup_message(self, surface):
        """Draw popup message"""
        if hasattr(self.play_screen, 'font'):
            text_surface = self.play_screen.font.render(self.play_screen.popup_message, True, (0, 255, 0))
            text_rect = text_surface.get_rect(center=(self.play_screen.width // 2, 50))
            surface.blit(text_surface, text_rect)
