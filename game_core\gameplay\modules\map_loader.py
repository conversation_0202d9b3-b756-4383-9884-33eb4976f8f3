"""
MapLoader - Handles map loading and processing

This module is responsible for:
- Finding and loading map files
- Processing different map formats (layered, single-layer, old format)
- Loading tile mappings and images
- Setting up map dimensions and properties
"""
import os
import json
import pygame


class MapLoader:
    """Handles map loading and processing operations"""

    def __init__(self, play_screen):
        """Initialize the MapLoader with reference to PlayScreen"""
        self.play_screen = play_screen

    def load_map(self, map_name):
        """Load a map from file"""
        # Reset the enemy manager to clear all enemies from the previous map
        self.play_screen.enemy_manager.enemies = []

        # Store whether we're teleporting before resetting flags
        was_teleporting = self.play_screen.is_teleporting
        teleport_info = self.play_screen.teleport_info

        self.play_screen.map_name = map_name

        # Find the map file
        map_path = self._find_map_file(map_name)
        if not map_path:
            return False

        try:
            # Load map data
            with open(map_path, 'r') as f:
                map_data = json.load(f)

            print(f"DEBUG: Loaded map data - map name in file: '{map_data.get('name', 'UNKNOWN')}'")
            print(f"DEBUG: Map dimensions: {map_data.get('width', 0)}x{map_data.get('height', 0)}")

            # Store map dimensions
            self.play_screen.map_width = map_data.get("width", 0)
            self.play_screen.map_height = map_data.get("height", 0)
            self.play_screen.map_data = map_data

            # Load collision data if available in the map file
            if "collision_data" in map_data:
                self.play_screen.collision_handler.load_collision_data(map_data["collision_data"])

            # Process map format
            self._process_map_format(map_data)

            # Restore teleportation flags
            self.play_screen.is_teleporting = was_teleporting
            self.play_screen.teleport_info = teleport_info

            return True
        except Exception as e:
            self.play_screen.status_message = f"Error loading map: {str(e)}"
            self.play_screen.status_timer = 180
            return False

    def _find_map_file(self, map_name):
        """Find the map file path"""
        # Get the current working directory
        current_dir = os.getcwd()
        print(f"Current working directory when loading map: {current_dir}")

        # Try to find the Maps directory
        maps_dir = self._find_maps_directory(current_dir)
        if not maps_dir:
            self.play_screen.status_message = f"Error: Maps directory not found"
            self.play_screen.status_timer = 180
            return None

        print(f"Using Maps directory when loading map: {maps_dir}")

        # Try to find the map file - it could be a main map or a related map
        main_map_path = os.path.join(maps_dir, map_name, f"{map_name}.json")
        print(f"DEBUG: Requested map name: '{map_name}'")
        print(f"DEBUG: Checking for main map file at: {main_map_path}")

        if os.path.exists(main_map_path):
            # It's a main map
            print(f"DEBUG: Found main map file: {main_map_path}")
            return main_map_path
        else:
            # It might be a related map, search in all map folders
            return self._find_related_map(maps_dir, map_name)

    def _find_maps_directory(self, current_dir):
        """Find the Maps directory"""
        # First check if Maps is in the current directory
        if os.path.exists(os.path.join(current_dir, "Maps")):
            return os.path.join(current_dir, "Maps")
        # Then check if Maps is in the parent directory
        elif os.path.exists(os.path.join(current_dir, "..", "Maps")):
            return os.path.join(current_dir, "..", "Maps")
        # Then check if Maps is in the grandparent directory
        elif os.path.exists(os.path.join(current_dir, "..", "..", "Maps")):
            return os.path.join(current_dir, "..", "..", "Maps")
        else:
            # Default to the relative path
            return "Maps" if os.path.exists("Maps") else None

    def _find_related_map(self, maps_dir, map_name):
        """Find a related map in subdirectories"""
        print(f"DEBUG: '{map_name}' is not a main map, searching in folders...")

        # Check if Maps directory exists
        if not os.path.exists(maps_dir):
            print(f"Maps directory does not exist: {maps_dir}")
            return None

        print(f"Maps directory exists: {maps_dir}")
        # List all folders in the Maps directory
        folders = [f for f in os.listdir(maps_dir) if os.path.isdir(os.path.join(maps_dir, f))]
        print(f"Found folders in Maps directory: {folders}")

        for folder_name in folders:
            folder_path = os.path.join(maps_dir, folder_name)
            print(f"Checking folder: {folder_path}")

            # List all files in this folder
            files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]
            print(f"Files in folder {folder_name}: {files}")

            # Check if this folder contains our map
            related_map_path = os.path.join(folder_path, f"{map_name}.json")
            print(f"Checking for related map file at: {related_map_path}")

            if os.path.exists(related_map_path):
                print(f"Found related map file: {related_map_path}")
                return related_map_path

        return None

    def _process_map_format(self, map_data):
        """Process the map based on its format"""
        # Check which format the map is in
        if "layers" in map_data and "tile_mapping" in map_data:
            # Layered format - process tile mapping and layers
            self._process_layered_format_map(map_data)
        elif "map_data" in map_data and "tile_mapping" in map_data:
            # Single-layer array format - process tile mapping and map data
            self._process_new_format_map(map_data)
        else:
            # Old format - process tiles directly
            self._process_old_format_map(map_data)

    def _process_layered_format_map(self, map_data):
        """Process a map in the layered format"""
        # Clear existing tiles
        self.play_screen.tiles = {}

        # Process tile mapping
        tile_mapping = map_data["tile_mapping"]
        self.play_screen.expanded_mapping = self._expand_tile_mapping(tile_mapping)

        # Load all tile images
        self._load_tile_images()

        # Add animated tiles to the expanded_mapping
        self._add_animated_tiles()

        # Store all layers separately instead of merging them
        self._process_layers(map_data)

    def _process_new_format_map(self, map_data):
        """Process a map in the new single-layer format"""
        # Clear existing tiles
        self.play_screen.tiles = {}

        # Process tile mapping
        tile_mapping = map_data["tile_mapping"]
        self.play_screen.expanded_mapping = self._expand_tile_mapping(tile_mapping)

        # Load all tile images
        self._load_tile_images()

        # Add animated tiles to the expanded_mapping
        self._add_animated_tiles()

        # Process single layer
        self._process_single_layer(map_data)

    def _process_old_format_map(self, map_data):
        """Process a map in the old format"""
        # Clear existing tiles
        self.play_screen.tiles = {}

        # Load tiles directly from map data
        self._load_old_format_tiles(map_data)

        # Create a single layer from the old format
        self._create_single_layer_from_old_format(map_data)

    def _expand_tile_mapping(self, tile_mapping):
        """Expand tile mapping patterns into individual tile entries"""
        expanded = {}

        for key, value in tile_mapping.items():
            if isinstance(value, dict) and value.get("type") == "loop":
                # This is a loop pattern, expand it
                start_id = value["start_id"]
                count = value["count"]
                pattern = value["pattern"]

                for i in range(count):
                    tile_id = start_id + i
                    # Generate the path using the pattern
                    path = pattern["prefix"] + str(pattern["start"] + i).zfill(pattern["digits"]) + pattern["suffix"]
                    expanded[str(tile_id)] = {
                        "path": path,
                        "tileset": value.get("tileset", 0)
                    }
            else:
                # This is a direct mapping
                expanded[key] = value

        return expanded

    def _load_tile_images(self):
        """Load all tile images from the expanded mapping"""
        for tile_id, tile_info in self.play_screen.expanded_mapping.items():
            path = tile_info["path"]
            try:
                # Skip animated tiles - they're handled separately
                if not path.startswith("animated:"):
                    # Skip enemy tiles - they're handled by the enemy manager
                    # Skip player character tiles - they're handled separately
                    if self._should_skip_tile(path):
                        continue
                    self.play_screen.tiles[int(tile_id)] = pygame.image.load(path).convert_alpha()
            except Exception as e:
                print(f"Error loading tile {path}: {e}")

    def _should_skip_tile(self, path):
        """Check if a tile should be skipped during loading"""
        skip_patterns = [
            "Enemies_Sprites/Phantom_Sprites",
            "Enemies_Sprites/Bomberplant_Sprites",
            "Enemies_Sprites/Spider_Sprites",
            "Enemies_Sprites/Pinkslime_Sprites",
            "Enemies_Sprites/Pinkbat_Sprites",
            "Enemies_Sprites/Spinner_Sprites",
            "character/char_idle_"
        ]

        for pattern in skip_patterns:
            if pattern in path:
                if "Enemies_Sprites" in pattern:
                    enemy_type = pattern.split("/")[1].replace("_Sprites", "").lower()
                    print(f"Skipping {enemy_type} enemy tile: {path}")
                elif "character/char_idle_" in pattern:
                    print(f"Skipping player character tile: {path}")
                return True
        return False

    def _add_animated_tiles(self):
        """Add animated tiles to the expanded mapping"""
        for tile_id, tile_name in self.play_screen.animated_tile_manager.animated_tile_ids.items():
            # Add the animated tile to the expanded mapping
            self.play_screen.expanded_mapping[str(tile_id)] = {
                "path": f"animated:{tile_name}",
                "tileset": -1,  # Special value for animated tiles
                "animated": True
            }

            # Check if this is a key item and add it to the key item manager
            if tile_name == "key_item":
                self.play_screen.key_item_id = tile_id
            # Check if this is a crystal item and add it to the crystal item manager
            elif tile_name == "crystal_item":
                self.play_screen.crystal_item_id = tile_id
            # Check if this is a lootchest item and add it to the lootchest manager
            elif tile_name == "lootchest_item":
                self.play_screen.lootchest_item_id = tile_id

    def _process_layers(self, map_data):
        """Process layers for layered format maps"""
        self.play_screen.layers = []
        width = map_data.get("width", 0)
        height = map_data.get("height", 0)

        # Process each layer
        for layer_idx, layer in enumerate(map_data.get("layers", [])):
            layer_visible = layer.get("visible", True)
            layer_data = layer.get("map_data", [])

            # Validate layer_data
            if not isinstance(layer_data, list) or not layer_data:
                print(f"Warning: Invalid layer data for layer {layer_idx}")
                continue

            # Ensure all rows have the same length
            row_length = len(layer_data[0]) if layer_data else 0
            for row_idx, row in enumerate(layer_data):
                if len(row) != row_length:
                    print(f"Warning: Row {row_idx} in layer {layer_idx} has inconsistent length")
                    # Pad or truncate the row to match the expected length
                    if len(row) < row_length:
                        layer_data[row_idx] = row + [-1] * (row_length - len(row))
                    else:
                        layer_data[row_idx] = row[:row_length]

            # Store the layer
            self.play_screen.layers.append({
                "visible": layer_visible,
                "data": layer_data
            })

    def _process_single_layer(self, map_data):
        """Process single layer for new format maps"""
        # Create a single layer from the map data
        layer_data = map_data.get("map_data", [])

        # Validate layer_data
        if not isinstance(layer_data, list) or not layer_data:
            print(f"Warning: Invalid map data")
            layer_data = []

        # Store as a single layer
        self.play_screen.layers = [{
            "visible": True,
            "data": layer_data
        }]

    def _load_old_format_tiles(self, map_data):
        """Load tiles for old format maps"""
        # In old format, tiles are stored directly in the map data
        tiles_data = map_data.get("tiles", {})

        for tile_id, tile_path in tiles_data.items():
            try:
                if not self._should_skip_tile(tile_path):
                    self.play_screen.tiles[int(tile_id)] = pygame.image.load(tile_path).convert_alpha()
            except Exception as e:
                print(f"Error loading old format tile {tile_path}: {e}")

    def _create_single_layer_from_old_format(self, map_data):
        """Create a single layer from old format map data"""
        # In old format, the map layout is stored directly
        map_layout = map_data.get("map", [])

        # Convert to layer format
        self.play_screen.layers = [{
            "visible": True,
            "data": map_layout
        }]