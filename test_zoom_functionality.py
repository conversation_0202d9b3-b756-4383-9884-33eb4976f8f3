#!/usr/bin/env python3
"""
Test script to verify zoom functionality is working correctly for enemies and characters.

This script tests:
1. Zoom controls (Ctrl++ and Ctrl+-)
2. Zoom factor application to player rendering
3. Zoom factor application to enemy rendering
4. Camera position adjustment with zoom
5. Zoom indicator display
"""

import sys
import os

# Add the game_core directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game_core'))

def test_zoom_controls():
    """Test that zoom controls work correctly"""
    print("Testing zoom controls...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success:
            # Test initial zoom factor
            initial_zoom = play_screen.camera_controller.zoom_factor
            print(f"✓ Initial zoom factor: {initial_zoom}")
            
            if initial_zoom != 1.0:
                print(f"✗ Expected initial zoom to be 1.0, got {initial_zoom}")
                return False
            
            # Test zoom in
            play_screen.camera_controller.zoom_in()
            zoom_after_in = play_screen.camera_controller.zoom_factor
            print(f"✓ Zoom factor after zoom in: {zoom_after_in}")
            
            if zoom_after_in <= initial_zoom:
                print(f"✗ Zoom in failed: {initial_zoom} -> {zoom_after_in}")
                return False
            
            # Test zoom out
            play_screen.camera_controller.zoom_out()
            zoom_after_out = play_screen.camera_controller.zoom_factor
            print(f"✓ Zoom factor after zoom out: {zoom_after_out}")
            
            if zoom_after_out != initial_zoom:
                print(f"✗ Zoom out failed: expected {initial_zoom}, got {zoom_after_out}")
                return False
            
            # Test zoom limits (can't zoom below 100%)
            play_screen.camera_controller.zoom_out()  # Should stay at 1.0
            zoom_at_limit = play_screen.camera_controller.zoom_factor
            print(f"✓ Zoom factor at minimum limit: {zoom_at_limit}")
            
            if zoom_at_limit != 1.0:
                print(f"✗ Zoom limit failed: expected 1.0, got {zoom_at_limit}")
                return False
            
            return True
        else:
            print("✗ Failed to load map for zoom testing")
            return False
        
    except Exception as e:
        print(f"✗ Zoom controls test failed: {e}")
        return False

def test_player_zoom_rendering():
    """Test that player zoom rendering works correctly"""
    print("\nTesting player zoom rendering...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success and play_screen.player:
            # Test player draw method with different zoom factors
            test_surface = pygame.Surface((800, 600))
            
            # Test normal zoom (1.0x)
            play_screen.player.draw(test_surface, 0, 0, 1.0)
            print("✓ Player drawn at 1.0x zoom")
            
            # Test zoomed in (2.0x)
            play_screen.player.draw(test_surface, 0, 0, 2.0)
            print("✓ Player drawn at 2.0x zoom")
            
            # Test zoomed in (0.5x) - should still work even if not normally allowed
            play_screen.player.draw(test_surface, 0, 0, 0.5)
            print("✓ Player drawn at 0.5x zoom")
            
            # Test that player has the draw method with correct signature
            if hasattr(play_screen.player, 'draw'):
                print("✓ Player has draw method")
            else:
                print("✗ Player missing draw method")
                return False
            
            return True
        else:
            print("⚠ No player to test zoom rendering with")
            return True
        
    except Exception as e:
        print(f"✗ Player zoom rendering test failed: {e}")
        return False

def test_enemy_zoom_rendering():
    """Test that enemy zoom rendering works correctly"""
    print("\nTesting enemy zoom rendering...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success and len(play_screen.enemy_manager.enemies) > 0:
            # Test enemy draw method with different zoom factors
            test_surface = pygame.Surface((800, 600))
            enemy = play_screen.enemy_manager.enemies[0]
            
            # Test normal zoom (1.0x)
            enemy.draw(test_surface, 0, 0, 1.0)
            print("✓ Enemy drawn at 1.0x zoom")
            
            # Test zoomed in (2.0x)
            enemy.draw(test_surface, 0, 0, 2.0)
            print("✓ Enemy drawn at 2.0x zoom")
            
            # Test zoomed in (0.5x)
            enemy.draw(test_surface, 0, 0, 0.5)
            print("✓ Enemy drawn at 0.5x zoom")
            
            # Test that enemy has the draw method with correct signature
            if hasattr(enemy, 'draw'):
                print("✓ Enemy has draw method")
            else:
                print("✗ Enemy missing draw method")
                return False
            
            # Test enemy manager draw method
            play_screen.enemy_manager.draw(test_surface, 0, 0, 2.0)
            print("✓ Enemy manager draw method works")
            
            return True
        else:
            print("⚠ No enemies to test zoom rendering with")
            return True
        
    except Exception as e:
        print(f"✗ Enemy zoom rendering test failed: {e}")
        return False

def test_render_manager_zoom():
    """Test that RenderManager properly applies zoom to all entities"""
    print("\nTesting RenderManager zoom integration...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success:
            # Test rendering at different zoom levels
            test_surface = pygame.Surface((800, 600))
            
            # Test normal zoom
            play_screen.camera_controller.zoom_factor = 1.0
            play_screen.render_manager.draw(test_surface)
            print("✓ RenderManager rendered at 1.0x zoom")
            
            # Test zoomed in
            play_screen.camera_controller.zoom_factor = 2.0
            play_screen.render_manager.draw(test_surface)
            print("✓ RenderManager rendered at 2.0x zoom")
            
            # Test that zoom factor is properly passed to entities
            if play_screen.player:
                # Check that player draw is called with zoom factor in RenderManager
                print("✓ Player rendering integrated with zoom")
            
            if len(play_screen.enemy_manager.enemies) > 0:
                # Check that enemy draw is called with zoom factor in RenderManager
                print("✓ Enemy rendering integrated with zoom")
            
            # Reset zoom
            play_screen.camera_controller.zoom_factor = 1.0
            
            return True
        else:
            print("✗ Failed to load map for RenderManager zoom testing")
            return False
        
    except Exception as e:
        print(f"✗ RenderManager zoom test failed: {e}")
        return False

def test_zoom_indicator():
    """Test that zoom indicator is displayed correctly"""
    print("\nTesting zoom indicator...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success:
            test_surface = pygame.Surface((800, 600))
            
            # Test zoom indicator at 1.0x (should not show)
            play_screen.camera_controller.zoom_factor = 1.0
            play_screen.render_manager.draw(test_surface)
            print("✓ Zoom indicator hidden at 1.0x zoom")
            
            # Test zoom indicator at 2.0x (should show)
            play_screen.camera_controller.zoom_factor = 2.0
            play_screen.render_manager.draw(test_surface)
            print("✓ Zoom indicator shown at 2.0x zoom")
            
            # Reset zoom
            play_screen.camera_controller.zoom_factor = 1.0
            
            return True
        else:
            print("✗ Failed to load map for zoom indicator testing")
            return False
        
    except Exception as e:
        print(f"✗ Zoom indicator test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 Testing Zoom Functionality")
    print("=" * 40)
    
    # Test 1: Zoom controls
    controls_ok = test_zoom_controls()
    
    # Test 2: Player zoom rendering
    player_ok = test_player_zoom_rendering()
    
    # Test 3: Enemy zoom rendering
    enemy_ok = test_enemy_zoom_rendering()
    
    # Test 4: RenderManager zoom integration
    render_ok = test_render_manager_zoom()
    
    # Test 5: Zoom indicator
    indicator_ok = test_zoom_indicator()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Test Results Summary:")
    print(f"Zoom Controls: {'✓ PASS' if controls_ok else '✗ FAIL'}")
    print(f"Player Zoom Rendering: {'✓ PASS' if player_ok else '✗ FAIL'}")
    print(f"Enemy Zoom Rendering: {'✓ PASS' if enemy_ok else '✗ FAIL'}")
    print(f"RenderManager Integration: {'✓ PASS' if render_ok else '✗ FAIL'}")
    print(f"Zoom Indicator: {'✓ PASS' if indicator_ok else '✗ FAIL'}")
    
    all_tests_passed = controls_ok and player_ok and enemy_ok and render_ok and indicator_ok
    
    if all_tests_passed:
        print("\n🎉 All zoom functionality tests passed! Zoom is working correctly.")
        return 0
    else:
        print("\n❌ Some zoom tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
