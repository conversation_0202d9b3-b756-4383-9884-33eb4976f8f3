"""
CollisionManager - Manages collision detection

This module is responsible for:
- Player-environment collision detection
- Enemy-environment collision detection
- Player-enemy collision detection
- Item collection collision detection
- Boundary collision detection
"""


class CollisionManager:
    """Handles collision detection and management"""

    def __init__(self, play_screen):
        """Initialize the CollisionManager with reference to PlayScreen"""
        self.play_screen = play_screen

    def check_player_collisions(self, original_x, original_y):
        """Check and handle player collisions"""
        if not self.play_screen.player:
            return

        collision_detected = False

        # Check for map boundary collisions
        if self._check_boundary_collision():
            collision_detected = True

        # Check for collisions with map tiles (check all layers, not just first)
        if hasattr(self.play_screen, 'layers') and self.play_screen.layers:
            for layer in self.play_screen.layers:
                if layer.get("visible", True):  # Only check visible layers
                    if self.play_screen.collision_handler.check_collision(
                        self.play_screen.player.rect,
                        self.play_screen.expanded_mapping,
                        layer["data"]
                    ):
                        collision_detected = True
                        break

        # If any collision was detected, revert to original position
        if collision_detected:
            self.play_screen.player.rect.x = original_x
            self.play_screen.player.rect.y = original_y

    def _check_boundary_collision(self):
        """Check if player is outside map boundaries"""
        if not self.play_screen.player:
            return False

        player_rect = self.play_screen.player.rect

        # Calculate map boundaries in pixels
        map_width_pixels = self.play_screen.map_width * self.play_screen.grid_cell_size
        map_height_pixels = self.play_screen.map_height * self.play_screen.grid_cell_size

        # Check if player is outside boundaries
        if (player_rect.left < 0 or
            player_rect.right > map_width_pixels or
            player_rect.top < 0 or
            player_rect.bottom > map_height_pixels):
            return True

        return False

    def check_enemy_collisions(self):
        """Check and handle enemy collisions"""
        if self.play_screen.player:
            # First, update all enemies with player position and collision data
            if hasattr(self.play_screen, 'layers') and self.play_screen.layers:
                self.play_screen.enemy_manager.update(
                    self.play_screen.player.rect.x,
                    self.play_screen.player.rect.y,
                    collision_handler=self.play_screen.collision_handler,
                    tile_mapping=self.play_screen.expanded_mapping,
                    map_data=self.play_screen.layers[0]["data"]
                )

            # Check for enemy-player collisions and apply knockback
            if hasattr(self.play_screen, 'layers') and self.play_screen.layers:
                self.play_screen.enemy_manager.check_player_collisions(
                    self.play_screen.player,
                    collision_handler=self.play_screen.collision_handler,
                    tile_mapping=self.play_screen.expanded_mapping,
                    map_data=self.play_screen.layers[0]["data"]
                )

            # Check if player's attack hits any enemies
            self.play_screen.enemy_manager.check_player_attacks(
                self.play_screen.player,
                collision_handler=self.play_screen.collision_handler,
                tile_mapping=self.play_screen.expanded_mapping,
                map_data=self.play_screen.layers[0]["data"]
            )

    def check_item_collisions(self):
        """Check for item collection collisions"""
        if not self.play_screen.player:
            return

        player_rect = self.play_screen.player.rect

        # Check for key item collection
        if hasattr(self.play_screen, 'key_item_manager'):
            self.play_screen.key_item_manager.check_player_collision(
                player_rect, self.play_screen.grid_cell_size
            )

        # Check for crystal item collection
        if hasattr(self.play_screen, 'crystal_item_manager'):
            self.play_screen.crystal_item_manager.check_player_collision(
                player_rect, self.play_screen.grid_cell_size
            )

    def check_relation_point_collisions(self):
        """Check for relation point (teleportation) collisions"""
        if not self.play_screen.player:
            return None

        # Check for collisions with relation points
        relation = self.play_screen.relation_handler.check_player_collision(self.play_screen.player.rect)
        return relation

    def check_lootchest_collisions(self):
        """Check for lootchest interaction collisions"""
        if not self.play_screen.player:
            return

        # Check for lootchest interactions
        if hasattr(self.play_screen, 'lootchest_manager'):
            # The lootchest hover detection is handled in the PlayScreen's is_hovering_lootchest method
            # and the actual interaction is handled in InputHandler's handle_right_click
            # This method is here for completeness but doesn't need to do anything special
            # since lootchest collision is primarily mouse-based, not player movement-based
            pass
