"""
CollisionManager - Manages collision detection

This module is responsible for:
- Player-environment collision detection
- Enemy-environment collision detection
- Player-enemy collision detection
- Item collection collision detection
- Boundary collision detection
"""


class CollisionManager:
    """Handles collision detection and management"""
    
    def __init__(self, play_screen):
        """Initialize the CollisionManager with reference to PlayScreen"""
        self.play_screen = play_screen
    
    def check_player_collisions(self, original_x, original_y):
        """Check and handle player collisions"""
        if not self.play_screen.player:
            return
            
        # Check for collisions with map tiles
        if hasattr(self.play_screen, 'layers') and self.play_screen.layers:
            # Check collision with the first layer (ground layer)
            if self.play_screen.collision_handler.check_collision(
                self.play_screen.player.rect,
                self.play_screen.expanded_mapping,
                self.play_screen.layers[0]["data"]
            ):
                # Collision detected, revert to original position
                self.play_screen.player.rect.x = original_x
                self.play_screen.player.rect.y = original_y
    
    def check_enemy_collisions(self):
        """Check and handle enemy collisions"""
        if self.play_screen.player:
            # Check for enemy-player collisions and apply knockback
            if hasattr(self.play_screen, 'layers') and self.play_screen.layers:
                self.play_screen.enemy_manager.check_player_collisions(
                    self.play_screen.player,
                    collision_handler=self.play_screen.collision_handler,
                    tile_mapping=self.play_screen.expanded_mapping,
                    map_data=self.play_screen.map_data
                )

            # Check if player's attack hits any enemies
            self.play_screen.enemy_manager.check_player_attacks(
                self.play_screen.player,
                collision_handler=self.play_screen.collision_handler,
                tile_mapping=self.play_screen.expanded_mapping,
                map_data=self.play_screen.map_data
            )
    
    def check_item_collisions(self):
        """Check for item collection collisions"""
        if not self.play_screen.player:
            return
            
        player_rect = self.play_screen.player.rect
        
        # Check for key item collection
        if hasattr(self.play_screen, 'key_item_manager'):
            self.play_screen.key_item_manager.check_collection(player_rect)
        
        # Check for crystal item collection
        if hasattr(self.play_screen, 'crystal_item_manager'):
            self.play_screen.crystal_item_manager.check_collection(player_rect)
    
    def check_relation_point_collisions(self):
        """Check for relation point (teleportation) collisions"""
        if not self.play_screen.player:
            return None
            
        # Check for collisions with relation points
        relation = self.play_screen.relation_handler.check_player_collision(self.play_screen.player.rect)
        return relation
