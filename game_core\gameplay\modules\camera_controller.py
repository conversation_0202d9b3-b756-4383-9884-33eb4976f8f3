"""
CameraController - Manages camera and viewport operations

This module is responsible for:
- Camera movement and positioning
- Zoom functionality
- Viewport calculations
- Center offset calculations for small maps
- Camera bounds checking
"""
import pygame


class CameraController:
    """Handles camera and viewport operations"""
    
    def __init__(self, play_screen):
        """Initialize the CameraController with reference to PlayScreen"""
        self.play_screen = play_screen
        
        # Zoom settings - limited to 100% minimum
        self.zoom_levels = [1.0, 1.5, 2.0, 3.0, 4.0]
        self.current_zoom_index = 0  # Start at 1.0x zoom (index 0)
        self.zoom_factor = self.zoom_levels[self.current_zoom_index]
        
        # Camera/viewport for large maps
        self.camera_x = 0
        self.camera_y = 0
        self.camera_speed = 5
        
        # Offset for centering small maps
        self.center_offset_x = 0
        self.center_offset_y = 0
    
    def update_camera(self, player):
        """Update camera position based on player position"""
        if not player:
            return
            
        # Calculate the desired camera position to center the player
        target_camera_x = player.rect.centerx - (self.play_screen.width // 2)
        target_camera_y = player.rect.centery - (self.play_screen.height // 2)
        
        # Apply zoom factor to camera positioning
        if self.zoom_factor != 1.0:
            # Adjust camera position for zoom
            zoom_offset_x = (self.play_screen.width * (self.zoom_factor - 1.0)) // (2 * self.zoom_factor)
            zoom_offset_y = (self.play_screen.height * (self.zoom_factor - 1.0)) // (2 * self.zoom_factor)
            target_camera_x += zoom_offset_x
            target_camera_y += zoom_offset_y
        
        # Clamp camera to map boundaries
        map_pixel_width = self.play_screen.map_width * self.play_screen.grid_cell_size
        map_pixel_height = self.play_screen.map_height * self.play_screen.grid_cell_size
        
        # Calculate effective screen size with zoom
        effective_screen_width = self.play_screen.width / self.zoom_factor
        effective_screen_height = self.play_screen.height / self.zoom_factor
        
        # Clamp camera position
        self.camera_x = max(0, min(target_camera_x, map_pixel_width - effective_screen_width))
        self.camera_y = max(0, min(target_camera_y, map_pixel_height - effective_screen_height))
        
        # If map is smaller than screen, center it
        if map_pixel_width < effective_screen_width:
            self.camera_x = -(effective_screen_width - map_pixel_width) // 2
        if map_pixel_height < effective_screen_height:
            self.camera_y = -(effective_screen_height - map_pixel_height) // 2
    
    def handle_zoom(self, event):
        """Handle zoom input events"""
        if event.type == pygame.KEYDOWN:
            # Handle zoom controls (Ctrl++ and Ctrl+-)
            keys = pygame.key.get_pressed()
            if keys[pygame.K_LCTRL] or keys[pygame.K_RCTRL]:
                if event.key == pygame.K_EQUALS or event.key == pygame.K_PLUS:  # Ctrl++ (zoom in)
                    self.zoom_in()
                elif event.key == pygame.K_MINUS:  # Ctrl+- (zoom out)
                    self.zoom_out()
    
    def zoom_in(self):
        """Zoom in to the next level"""
        if self.current_zoom_index < len(self.zoom_levels) - 1:
            self.current_zoom_index += 1
            self.zoom_factor = self.zoom_levels[self.current_zoom_index]
            self._update_grid_cell_size()
            print(f"Zoomed in to {self.zoom_factor}x")
    
    def zoom_out(self):
        """Zoom out to the previous level (minimum 100%)"""
        if self.current_zoom_index > 0:
            self.current_zoom_index -= 1
            self.zoom_factor = self.zoom_levels[self.current_zoom_index]
            self._update_grid_cell_size()
            print(f"Zoomed out to {self.zoom_factor}x")
    
    def _update_grid_cell_size(self):
        """Update grid cell size based on zoom factor"""
        self.play_screen.grid_cell_size = int(self.play_screen.base_grid_cell_size * self.zoom_factor)
        
        # Update collision handler with new grid size
        if hasattr(self.play_screen, 'collision_handler'):
            self.play_screen.collision_handler.grid_cell_size = self.play_screen.grid_cell_size
        
        # Update relation handler with new grid size
        if hasattr(self.play_screen, 'relation_handler'):
            self.play_screen.relation_handler.grid_cell_size = self.play_screen.grid_cell_size
    
    def calculate_center_offset(self):
        """Calculate center offset for small maps"""
        # Calculate the pixel dimensions of the map
        map_pixel_width = self.play_screen.map_width * self.play_screen.grid_cell_size
        map_pixel_height = self.play_screen.map_height * self.play_screen.grid_cell_size
        
        # Calculate center offset if map is smaller than screen
        if map_pixel_width < self.play_screen.width:
            self.center_offset_x = (self.play_screen.width - map_pixel_width) // 2
        else:
            self.center_offset_x = 0
            
        if map_pixel_height < self.play_screen.height:
            self.center_offset_y = (self.play_screen.height - map_pixel_height) // 2
        else:
            self.center_offset_y = 0
    
    def get_camera_position(self):
        """Get current camera position"""
        return self.camera_x, self.camera_y
    
    def get_center_offset(self):
        """Get current center offset"""
        return self.center_offset_x, self.center_offset_y
    
    def get_zoom_factor(self):
        """Get current zoom factor"""
        return self.zoom_factor
    
    def set_camera_position(self, x, y):
        """Set camera position directly"""
        self.camera_x = x
        self.camera_y = y
    
    def world_to_screen(self, world_x, world_y):
        """Convert world coordinates to screen coordinates"""
        screen_x = (world_x - self.camera_x + self.center_offset_x) * self.zoom_factor
        screen_y = (world_y - self.camera_y + self.center_offset_y) * self.zoom_factor
        return screen_x, screen_y
    
    def screen_to_world(self, screen_x, screen_y):
        """Convert screen coordinates to world coordinates"""
        world_x = (screen_x / self.zoom_factor) + self.camera_x - self.center_offset_x
        world_y = (screen_y / self.zoom_factor) + self.camera_y - self.center_offset_y
        return world_x, world_y
    
    def is_visible(self, world_x, world_y, width, height):
        """Check if a world rectangle is visible on screen"""
        # Convert to screen coordinates
        screen_x, screen_y = self.world_to_screen(world_x, world_y)
        screen_width = width * self.zoom_factor
        screen_height = height * self.zoom_factor
        
        # Check if rectangle intersects with screen
        return (screen_x + screen_width >= 0 and 
                screen_x <= self.play_screen.width and
                screen_y + screen_height >= 0 and 
                screen_y <= self.play_screen.height)
