"""
InputHandler - Processes events and input

This module is responsible for:
- Event handling and processing
- Mouse input handling
- Keyboard input handling
- Inventory interaction handling
- Attack and interaction input
- UI event handling
"""
import pygame


class InputHandler:
    """Handles input events and processing"""
    
    def __init__(self, play_screen):
        """Initialize the InputHandler with reference to PlayScreen"""
        self.play_screen = play_screen
    
    def handle_event(self, event):
        """Handle events for the play screen"""
        mouse_pos = pygame.mouse.get_pos()

        # If game over screen is showing, handle its events
        if self.play_screen.show_game_over:
            return self._handle_game_over_events(event)

        # Handle common events (back button also saves)
        result = self._handle_common_events(event, mouse_pos)
        if result:
            return result

        # Handle zoom controls
        self.play_screen.camera_controller.handle_zoom(event)

        # Handle inventory toggle
        if self._handle_inventory_toggle(event):
            return None

        # Handle mouse events
        return self._handle_mouse_events(event, mouse_pos)
    
    def _handle_game_over_events(self, event):
        """Handle events when game over screen is showing"""
        result = self.play_screen.game_over_screen.handle_event(event)
        if result == "restart":
            # Reload the current map to restart
            self.play_screen.reload_current_map()
            return None
        elif result == "exit":
            # Return to map selection
            return "back"
        return None
    
    def _handle_common_events(self, event, mouse_pos):
        """Handle common events like back button"""
        # Update back button
        self.play_screen.back_button.update(mouse_pos)

        # Check for button clicks
        if event.type == pygame.MOUSEBUTTONDOWN:
            # Check if back button was clicked
            if self.play_screen.back_button.is_clicked(event):
                return self._handle_back_button_click()
        
        return None
    
    def _handle_back_button_click(self):
        """Handle back button click with auto-save"""
        # Auto-save the game before returning to map selection
        if (self.play_screen.player and 
            not self.play_screen.player.is_dead and 
            self.play_screen.map_name):
            
            # Save relation points and game state
            self._save_relation_points()
            
            # Save the game
            self.play_screen.save_game()
        
        return "back"
    
    def _save_relation_points(self):
        """Save relation points before exiting"""
        if hasattr(self.play_screen, 'relation_handler'):
            print(f"Saving relation points for map: {self.play_screen.map_name}")
            print(f"Current map in relation handler: {self.play_screen.relation_handler.current_map}")
            print(f"All relation points: {self.play_screen.relation_handler.relation_points}")

            # Store relation points for the GameStateSaver to handle
            if self.play_screen.map_name in self.play_screen.relation_handler.relation_points:
                relation_points = self.play_screen.relation_handler.relation_points[self.play_screen.map_name]
                print(f"Found relation points using map_name: {self.play_screen.map_name}")
            elif self.play_screen.relation_handler.current_map in self.play_screen.relation_handler.relation_points:
                relation_points = self.play_screen.relation_handler.relation_points[self.play_screen.relation_handler.current_map]
                print(f"Found relation points using current_map: {self.play_screen.relation_handler.current_map}")
            else:
                print(f"No relation points found for map: {self.play_screen.map_name}")
                relation_points = {}

            # Store the relation points for GameStateSaver to use
            self.play_screen.current_relation_points = relation_points
            print(f"Prepared relation points for saving: {relation_points}")
    
    def _handle_inventory_toggle(self, event):
        """Handle inventory toggle events"""
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                # Toggle player inventory visibility
                if self.play_screen.player_inventory.is_visible():
                    self.play_screen.player_inventory.hide()
                else:
                    self.play_screen.player_inventory.show()
                return True
        return False
    
    def _handle_mouse_events(self, event, mouse_pos):
        """Handle mouse events"""
        # Pass mouse events to player character for attack handling
        if self.play_screen.player and event.type == pygame.MOUSEBUTTONDOWN:
            return self._handle_mouse_button_down(event, mouse_pos)
        return None
    
    def _handle_mouse_button_down(self, event, mouse_pos):
        """Handle mouse button down events"""
        # Handle mouse wheel for inventory selection
        if event.button == 4:  # Mouse wheel up
            self.play_screen.hud.inventory.selected_slot = (
                self.play_screen.hud.inventory.selected_slot - 1
            ) % self.play_screen.hud.inventory.num_slots
        elif event.button == 5:  # Mouse wheel down
            self.play_screen.hud.inventory.selected_slot = (
                self.play_screen.hud.inventory.selected_slot + 1
            ) % self.play_screen.hud.inventory.num_slots
        # Handle left-click for inventory slots, player inventory, and attacks
        elif event.button == 1:  # Left mouse button
            return self._handle_left_click(event, mouse_pos)
        # Handle right-click for inventory item picking or lootchest interaction
        elif event.button == 3:  # Right mouse button
            return self._handle_right_click(event, mouse_pos)
        
        return None
    
    def _handle_left_click(self, event, mouse_pos):
        """Handle left mouse button clicks"""
        # Check if chest inventory is visible and handle clicks
        if self.play_screen.chest_inventory.is_visible():
            # Handle click in chest inventory
            self.play_screen.chest_inventory.handle_click(mouse_pos)
            # Also check player inventory if it's visible
            if self.play_screen.player_inventory.is_visible():
                self.play_screen.player_inventory.handle_click(mouse_pos)
        # Check if only player inventory is visible and handle clicks
        elif self.play_screen.player_inventory.is_visible():
            # Handle click in player inventory (but don't close it on click outside)
            self.play_screen.player_inventory.handle_click(mouse_pos)
        # Check if clicking on an inventory slot
        elif self.play_screen.hud.inventory.hovered_slot != -1:
            # Select the clicked slot
            self.play_screen.hud.inventory.selected_slot = self.play_screen.hud.inventory.hovered_slot
        else:
            # No inventory slot clicked, use left-click for attack
            self.play_screen.player.handle_mouse_event(event)
        
        return None
    
    def _handle_right_click(self, event, mouse_pos):
        """Handle right mouse button clicks"""
        # Check if clicking on a lootchest
        if hasattr(self.play_screen, 'layers') and self.play_screen.layers:
            # Convert mouse position to world coordinates
            world_x, world_y = self.play_screen.camera_controller.screen_to_world(mouse_pos[0], mouse_pos[1])
            
            # Check if right-clicking on a lootchest
            grid_x = int(world_x // self.play_screen.grid_cell_size)
            grid_y = int(world_y // self.play_screen.grid_cell_size)
            
            # Check all layers for lootchest tiles
            for layer in self.play_screen.layers:
                if (layer["visible"] and 
                    0 <= grid_y < len(layer["data"]) and 
                    0 <= grid_x < len(layer["data"][grid_y])):
                    
                    tile_id = layer["data"][grid_y][grid_x]
                    if (hasattr(self.play_screen, 'lootchest_item_id') and 
                        tile_id == self.play_screen.lootchest_item_id):
                        # Right-clicked on a lootchest
                        self.play_screen.lootchest_manager.handle_right_click(grid_x, grid_y)
                        return None
        
        # Check if clicking on an inventory slot for item picking
        if self.play_screen.hud.inventory.hovered_slot != -1:
            # Right-click on inventory slot - pick up item
            slot_index = self.play_screen.hud.inventory.hovered_slot
            item = self.play_screen.hud.inventory.get_item_at_slot(slot_index)
            if item:
                # Remove item from inventory and add to player inventory
                self.play_screen.hud.inventory.remove_item_at_slot(slot_index)
                self.play_screen.player_inventory.add_item(item)
                print(f"Moved item {item} from HUD to player inventory")
        
        return None
    
    def update_mouse_hover(self, mouse_pos):
        """Update mouse hover states"""
        # Update inventory hover state
        if hasattr(self.play_screen, 'hud') and self.play_screen.hud:
            self.play_screen.hud.inventory.update_hover(mouse_pos)
