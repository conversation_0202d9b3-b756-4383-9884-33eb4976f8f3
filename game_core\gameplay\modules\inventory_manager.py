"""
InventoryManager - Manages inventory and items

This module is responsible for:
- Inventory system coordination
- Item collection and management
- Chest interaction handling
- Inventory UI coordination
- Item transfer between inventories
"""


class InventoryManager:
    """Handles inventory and item management"""
    
    def __init__(self, play_screen):
        """Initialize the InventoryManager with reference to PlayScreen"""
        self.play_screen = play_screen
    
    def update_inventories(self, mouse_pos):
        """Update all inventory systems"""
        # Update chest inventory
        if self.play_screen.chest_inventory.is_visible():
            self.play_screen.chest_inventory.update(mouse_pos)

        # Update player inventory
        if self.play_screen.player_inventory.is_visible():
            self.play_screen.player_inventory.update(mouse_pos)
    
    def handle_chest_opened(self, chest_x, chest_y, contents):
        """Handle when a chest is opened"""
        print(f"Chest opened at ({chest_x}, {chest_y}) with contents: {contents}")
        
        # Show the chest inventory with the contents
        self.play_screen.chest_inventory.show_chest_contents(contents, chest_x, chest_y)
        
        # Also show the player inventory so items can be transferred
        self.play_screen.player_inventory.show()
    
    def check_item_collections(self):
        """Check for item collection collisions"""
        if not self.play_screen.player:
            return
            
        player_rect = self.play_screen.player.rect
        
        # Check for key item collection
        if hasattr(self.play_screen, 'key_item_manager'):
            collected_keys = self.play_screen.key_item_manager.check_collection(player_rect)
            for key_pos in collected_keys:
                self._handle_key_collected(key_pos)
        
        # Check for crystal item collection
        if hasattr(self.play_screen, 'crystal_item_manager'):
            collected_crystals = self.play_screen.crystal_item_manager.check_collection(player_rect)
            for crystal_pos in collected_crystals:
                self._handle_crystal_collected(crystal_pos)
    
    def _handle_key_collected(self, key_pos):
        """Handle key item collection"""
        print(f"Key collected at position: {key_pos}")
        # Add key to inventory or handle key collection logic
        if hasattr(self.play_screen, 'hud') and self.play_screen.hud:
            self.play_screen.hud.inventory.add_item("key")
    
    def _handle_crystal_collected(self, crystal_pos):
        """Handle crystal item collection"""
        print(f"Crystal collected at position: {crystal_pos}")
        # Add crystal to inventory or handle crystal collection logic
        if hasattr(self.play_screen, 'hud') and self.play_screen.hud:
            self.play_screen.hud.inventory.add_item("crystal")
    
    def transfer_item_to_player_inventory(self, item):
        """Transfer item to player inventory"""
        if self.play_screen.player_inventory:
            success = self.play_screen.player_inventory.add_item(item)
            if success:
                print(f"Item {item} transferred to player inventory")
            else:
                print(f"Failed to transfer item {item} - inventory might be full")
            return success
        return False
    
    def transfer_item_to_hud_inventory(self, item):
        """Transfer item to HUD inventory"""
        if hasattr(self.play_screen, 'hud') and self.play_screen.hud:
            success = self.play_screen.hud.inventory.add_item(item)
            if success:
                print(f"Item {item} transferred to HUD inventory")
            else:
                print(f"Failed to transfer item {item} - HUD inventory might be full")
            return success
        return False
    
    def get_hud_inventory(self):
        """Get the HUD inventory"""
        if hasattr(self.play_screen, 'hud') and self.play_screen.hud:
            return self.play_screen.hud.inventory
        return None
    
    def get_player_inventory(self):
        """Get the player inventory"""
        return self.play_screen.player_inventory
    
    def get_chest_inventory(self):
        """Get the chest inventory"""
        return self.play_screen.chest_inventory
    
    def is_any_inventory_visible(self):
        """Check if any inventory is currently visible"""
        return (self.play_screen.chest_inventory.is_visible() or 
                self.play_screen.player_inventory.is_visible())
    
    def hide_all_inventories(self):
        """Hide all inventories"""
        self.play_screen.chest_inventory.hide()
        self.play_screen.player_inventory.hide()
    
    def toggle_player_inventory(self):
        """Toggle player inventory visibility"""
        if self.play_screen.player_inventory.is_visible():
            self.play_screen.player_inventory.hide()
        else:
            self.play_screen.player_inventory.show()
