"""
TeleportationManager - Handles teleportation and relation points

This module is responsible for:
- Relation point loading and management
- Teleportation logic and execution
- Map switching during teleportation
- Player positioning after teleportation
- Teleportation state management
"""


class TeleportationManager:
    """Handles teleportation and relation points"""
    
    def __init__(self, play_screen):
        """Initialize the TeleportationManager with reference to PlayScreen"""
        self.play_screen = play_screen
    
    def load_relation_points(self, map_data):
        """Load relation points for the current map"""
        # First, load all relation points from all maps to ensure we have a complete set
        print(f"Loading all relation points for teleportation")
        self.play_screen.relation_handler.load_all_relation_points()

        # Then load relation points for the current map if available
        if "relation_points" in map_data:
            print(f"Found relation points in map data: {map_data['relation_points']}")
            self.play_screen.relation_handler.load_relation_points(
                self.play_screen.map_name, 
                map_data["relation_points"]
            )
        else:
            # Load relation points from file (will search for other maps with relation points)
            print(f"No relation points in map data, loading from file")
            self.play_screen.relation_handler.load_relation_points(self.play_screen.map_name)

        # Make sure the current map is set correctly in both handlers
        self.play_screen.relation_handler.current_map = self.play_screen.map_name
        self.play_screen.lootchest_manager.set_current_map(self.play_screen.map_name)
        print(f"Current map set to: {self.play_screen.relation_handler.current_map}")
        print(f"All loaded relation points: {self.play_screen.relation_handler.relation_points}")
    
    def handle_teleportation(self, relation):
        """Handle teleportation when player touches a relation point"""
        print(f"Player touched relation point: {relation['from_point']} -> {relation['to_point']} in map {relation['to_map']}")
        print(f"Teleporting to position: {relation['to_position']}")

        # Save current player location before teleporting
        self._save_current_location()

        # Set teleportation flags
        self.play_screen.is_teleporting = True
        self.play_screen.teleport_info = {
            'target_map': relation['to_map'],
            'target_position': relation['to_position'],
            'target_point': relation['to_point']
        }

        # Save target location
        self._save_target_location(relation)

        # Return the target map to trigger map loading
        return relation['to_map']
    
    def _save_current_location(self):
        """Save current player location before teleporting"""
        # Determine which folder (world) the current map belongs to
        current_folder_name = self.play_screen.player_location_tracker._determine_folder_name(self.play_screen.map_name)

        print(f"DEBUG: Saving player location for world {current_folder_name}, map {self.play_screen.map_name}")

        # Save this position to the player_location_tracker
        self.play_screen.player_location_tracker.save_location(
            self.play_screen.map_name,
            self.play_screen.player.rect.x,
            self.play_screen.player.rect.y,
            self.play_screen.player.direction,
            self.play_screen.player.current_health,
            self.play_screen.player.shield_durability,
            current_folder_name
        )
    
    def _save_target_location(self, relation):
        """Save target location for teleportation"""
        target_map = relation['to_map']
        target_position = relation['to_position']

        # Determine which folder (world) the target map belongs to
        target_folder_name = self.play_screen.player_location_tracker._determine_folder_name(target_map)

        print(f"DEBUG: Saving player location for world {target_folder_name}, map {target_map}")

        # Save this position to the player_location_tracker
        self.play_screen.player_location_tracker.save_location(
            target_map,
            target_position[0],
            target_position[1],
            self.play_screen.player.direction,
            self.play_screen.player.current_health,
            self.play_screen.player.shield_durability,
            target_folder_name
        )

        # Save to file
        self.play_screen.player_location_tracker.save_to_file()
    
    def complete_teleportation(self):
        """Complete teleportation after map loading"""
        if not self.play_screen.is_teleporting or not self.play_screen.teleport_info:
            return

        # Set player position to the teleport target
        target_position = self.play_screen.teleport_info['target_position']
        if self.play_screen.player:
            self.play_screen.player.rect.x = target_position[0]
            self.play_screen.player.rect.y = target_position[1]
            print(f"Player teleported to position: ({target_position[0]}, {target_position[1]})")

        # Clear teleportation flags
        self.play_screen.is_teleporting = False
        self.play_screen.teleport_info = None
    
    def is_teleporting(self):
        """Check if currently teleporting"""
        return self.play_screen.is_teleporting
    
    def get_teleport_info(self):
        """Get current teleportation info"""
        return self.play_screen.teleport_info
