"""
GameStateManager - Handles save/load operations

This module is responsible for:
- Game state saving and loading
- Enemy data loading from saved state
- Inventory data loading from saved state
- Collected items loading from saved state
- Character inventory management
- Integration with GameStateSaver and CharacterInventorySaver
"""


class GameStateManager:
    """Handles game state save/load operations"""
    
    def __init__(self, play_screen):
        """Initialize the GameStateManager with reference to PlayScreen"""
        self.play_screen = play_screen
    
    def load_game_state_from_map(self, map_data):
        """Load game state from map data if available"""
        enemies_loaded = False
        
        if "game_state" in map_data:
            game_state = map_data["game_state"]
            
            # Load enemies from saved game state
            if "enemies" in game_state:
                # Standard format with descriptive keys
                self._load_enemies_from_game_state(game_state["enemies"])
                enemies_loaded = True
            elif "e" in game_state:
                # Legacy compact format
                self._load_enemies_from_game_state(game_state["e"])
                enemies_loaded = True

            # Load inventory data if available
            if "inventory" in game_state:
                self._load_inventory_from_game_state(game_state["inventory"])

            # Load collected keys data if available
            if "collected_keys" in game_state:
                self._load_collected_keys_from_game_state(game_state["collected_keys"])

            # Load collected crystals data if available
            if "collected_crystals" in game_state:
                self._load_collected_crystals_from_game_state(game_state["collected_crystals"])

            # Load opened lootchests data if available
            if "opened_lootchests" in game_state:
                self._load_opened_lootchests_from_game_state(game_state["opened_lootchests"])

            # Load chest contents data if available
            if "chest_contents" in game_state:
                self._load_chest_contents_from_game_state(game_state["chest_contents"])
        
        # Only load enemies from map data if they weren't loaded from game state
        if not enemies_loaded and "enemies" in map_data:
            self.play_screen.enemy_manager.load_enemies_from_map(map_data)
    
    def _load_enemies_from_game_state(self, enemies_data):
        """Load enemies from saved game state"""
        print(f"Clearing {len(self.play_screen.enemy_manager.enemies)} existing enemies before loading from game state")
        self.play_screen.enemy_manager.enemies = []
        
        print(f"Loading {len(enemies_data)} enemies from game state")
        for enemy_data in enemies_data:
            try:
                # Handle both dictionary and list formats
                if isinstance(enemy_data, dict):
                    self._load_enemy_from_dict(enemy_data)
                elif isinstance(enemy_data, list):
                    self._load_enemy_from_list(enemy_data)
            except Exception as e:
                print(f"Error loading enemy from game state: {e}")
                print(f"Enemy data: {enemy_data}")
    
    def _load_enemy_from_dict(self, enemy_data):
        """Load enemy from dictionary format"""
        enemy_type = enemy_data.get("type", "phantom")
        position = enemy_data.get("position", {"x": 0, "y": 0})
        direction = enemy_data.get("direction", "left")
        health = enemy_data.get("health", 100)
        state = enemy_data.get("state", "idle")
        
        # Create enemy
        enemy = self.play_screen.enemy_manager.create_enemy(
            enemy_type, position["x"], position["y"]
        )
        
        if enemy:
            enemy.direction = direction
            enemy.state = state
            
            # Set health appropriately
            if hasattr(enemy, 'current_health'):
                enemy.current_health = health
            elif hasattr(enemy, 'health'):
                enemy.health = health
            
            # Set float position if available
            float_pos = enemy_data.get("float_position")
            if float_pos and hasattr(enemy, 'float_x') and hasattr(enemy, 'float_y'):
                enemy.float_x = float_pos["x"]
                enemy.float_y = float_pos["y"]
    
    def _load_enemy_from_list(self, enemy_data):
        """Load enemy from list format (legacy)"""
        if len(enemy_data) < 6:
            print(f"Invalid enemy data format: {enemy_data}")
            return
        
        enemy_type = enemy_data[0]
        x = enemy_data[1]
        y = enemy_data[2]
        direction = enemy_data[3]
        health = enemy_data[4]
        state = enemy_data[5]
        
        # Create enemy
        enemy = self.play_screen.enemy_manager.create_enemy(enemy_type, x, y)
        
        if enemy:
            enemy.direction = direction
            enemy.state = state
            
            # Set health appropriately
            if hasattr(enemy, 'current_health'):
                enemy.current_health = health
            elif hasattr(enemy, 'health'):
                enemy.health = health
            
            # Set float position if available
            if len(enemy_data) > 7 and hasattr(enemy, 'float_x') and hasattr(enemy, 'float_y'):
                enemy.float_x = enemy_data[6]
                enemy.float_y = enemy_data[7]
    
    def _load_inventory_from_game_state(self, inventory_data):
        """Load inventory from saved game state"""
        if hasattr(self.play_screen, 'hud') and self.play_screen.hud:
            # Clear existing inventory
            self.play_screen.hud.inventory.clear()
            
            # Load items
            for item in inventory_data:
                self.play_screen.hud.inventory.add_item(item)
    
    def _load_collected_keys_from_game_state(self, collected_keys_data):
        """Load collected keys from saved game state"""
        if hasattr(self.play_screen, 'key_item_manager'):
            self.play_screen.key_item_manager.collected_keys = set(collected_keys_data)
    
    def _load_collected_crystals_from_game_state(self, collected_crystals_data):
        """Load collected crystals from saved game state"""
        if hasattr(self.play_screen, 'crystal_item_manager'):
            self.play_screen.crystal_item_manager.collected_crystals = set(collected_crystals_data)
    
    def _load_opened_lootchests_from_game_state(self, opened_lootchests_data):
        """Load opened lootchests from saved game state"""
        if hasattr(self.play_screen, 'lootchest_manager'):
            self.play_screen.lootchest_manager.opened_chests = set(opened_lootchests_data)
    
    def _load_chest_contents_from_game_state(self, chest_contents_data):
        """Load chest contents from saved game state"""
        if hasattr(self.play_screen, 'lootchest_manager'):
            self.play_screen.lootchest_manager.chest_contents = chest_contents_data
    
    def save_game(self):
        """Save the current game state to the map file"""
        # Save map state
        map_success, map_error_message = self.play_screen.game_state_saver.save_game_state(self.play_screen)

        # Save character inventory separately
        inventory_success, inventory_error_message = self.save_character_inventory()

        # Save current player location
        self.play_screen.player_manager.save_player_location()

        # Return results without showing popup messages
        if map_success and inventory_success:
            return True
        elif not map_success:
            # Only log error, don't show message
            print(f"Error saving game: {map_error_message}")
            return False
        else:
            # Only log error, don't show message
            print(f"Error saving inventory: {inventory_error_message}")
            return False
    
    def save_character_inventory(self):
        """Save the character's inventory to a separate file"""
        # Skip if player inventory is not initialized
        if not self.play_screen.player_inventory:
            return False, "Player inventory not initialized"

        # Save the inventory data
        return self.play_screen.character_inventory_saver.save_inventory(self.play_screen.player_inventory)
    
    def load_character_inventory(self):
        """Load character inventory data from separate save file"""
        # Skip if player inventory is not initialized
        if not self.play_screen.player_inventory:
            print("Player inventory not initialized, skipping character inventory load")
            return

        # Load the inventory data
        success, error_message = self.play_screen.character_inventory_saver.load_inventory(self.play_screen.player_inventory)
        
        if not success:
            print(f"Could not load character inventory: {error_message}")
        else:
            print("Character inventory loaded successfully")
