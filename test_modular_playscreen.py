#!/usr/bin/env python3
"""
Test script to verify the modular PlayScreen architecture works correctly.

This script tests:
1. Module imports work correctly
2. PlayScreen can be instantiated with modular components
3. Basic functionality is preserved
"""

import sys
import os

# Add the game_core directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game_core'))

def test_module_imports():
    """Test that all modular components can be imported"""
    print("Testing module imports...")

    try:
        from gameplay.modules.map_loader import MapLoader
        print("✓ MapLoader imported successfully")
    except ImportError as e:
        print(f"✗ MapLoader import failed: {e}")
        return False

    try:
        from gameplay.modules.camera_controller import CameraController
        print("✓ CameraController imported successfully")
    except ImportError as e:
        print(f"✗ CameraController import failed: {e}")
        return False

    try:
        from gameplay.modules.player_manager import PlayerManager
        print("✓ PlayerManager imported successfully")
    except ImportError as e:
        print(f"✗ PlayerManager import failed: {e}")
        return False

    try:
        from gameplay.modules.game_state_manager import GameStateManager
        print("✓ GameStateManager imported successfully")
    except ImportError as e:
        print(f"✗ GameStateManager import failed: {e}")
        return False

    try:
        from gameplay.modules.input_handler import InputHandler
        print("✓ InputHandler imported successfully")
    except ImportError as e:
        print(f"✗ InputHandler import failed: {e}")
        return False

    try:
        from gameplay.modules.render_manager import RenderManager
        print("✓ RenderManager imported successfully")
    except ImportError as e:
        print(f"✗ RenderManager import failed: {e}")
        return False

    try:
        from gameplay.modules.collision_manager import CollisionManager
        print("✓ CollisionManager imported successfully")
    except ImportError as e:
        print(f"✗ CollisionManager import failed: {e}")
        return False

    try:
        from gameplay.modules.teleportation_manager import TeleportationManager
        print("✓ TeleportationManager imported successfully")
    except ImportError as e:
        print(f"✗ TeleportationManager import failed: {e}")
        return False

    try:
        from gameplay.modules.inventory_manager import InventoryManager
        print("✓ InventoryManager imported successfully")
    except ImportError as e:
        print(f"✗ InventoryManager import failed: {e}")
        return False

    return True

def test_playscreen_instantiation():
    """Test that PlayScreen can be instantiated with modular components"""
    print("\nTesting PlayScreen instantiation...")

    try:
        import pygame
        pygame.init()

        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)

        from gameplay.play_screen import PlayScreen

        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        print("✓ PlayScreen instantiated successfully")

        # Check that modular components are present
        components = [
            'map_loader', 'camera_controller', 'player_manager',
            'game_state_manager', 'input_handler', 'render_manager',
            'collision_manager', 'teleportation_manager', 'inventory_manager'
        ]

        for component in components:
            if hasattr(play_screen, component):
                print(f"✓ {component} component present")
            else:
                print(f"✗ {component} component missing")
                return False

        return True

    except Exception as e:
        print(f"✗ PlayScreen instantiation failed: {e}")
        return False

def test_component_functionality():
    """Test basic functionality of modular components"""
    print("\nTesting component functionality...")

    try:
        import pygame
        pygame.init()

        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)

        from gameplay.play_screen import PlayScreen

        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)

        # Test CameraController
        zoom_factor = play_screen.camera_controller.get_zoom_factor()
        print(f"✓ CameraController zoom factor: {zoom_factor}")

        # Test camera position
        camera_x, camera_y = play_screen.camera_controller.get_camera_position()
        print(f"✓ CameraController position: ({camera_x}, {camera_y})")

        # Test center offset
        offset_x, offset_y = play_screen.camera_controller.get_center_offset()
        print(f"✓ CameraController center offset: ({offset_x}, {offset_y})")

        return True

    except Exception as e:
        print(f"✗ Component functionality test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🎮 Testing Modular PlayScreen Architecture")
    print("=" * 50)

    # Test 1: Module imports
    imports_ok = test_module_imports()

    # Test 2: PlayScreen instantiation
    instantiation_ok = test_playscreen_instantiation()

    # Test 3: Component functionality
    functionality_ok = test_component_functionality()

    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"Module Imports: {'✓ PASS' if imports_ok else '✗ FAIL'}")
    print(f"PlayScreen Instantiation: {'✓ PASS' if instantiation_ok else '✗ FAIL'}")
    print(f"Component Functionality: {'✓ PASS' if functionality_ok else '✗ FAIL'}")

    all_tests_passed = imports_ok and instantiation_ok and functionality_ok

    if all_tests_passed:
        print("\n🎉 All tests passed! Modular architecture is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
