#!/usr/bin/env python3
"""
Test script to verify the inventory loading fix works correctly.

This script tests:
1. GameStateManager can load inventory data without errors
2. InventoryManager can handle item collection properly
3. InputHandler can handle inventory interactions
"""

import sys
import os

# Add the game_core directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game_core'))

def test_inventory_loading():
    """Test that inventory loading works without errors"""
    print("Testing inventory loading...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Test inventory loading with sample data
        sample_inventory_data = [
            {"name": "Key", "count": 2, "image": None},
            {"name": "Crystal", "count": 1, "image": None},
            {"name": "Potion", "count": 5, "image": None}
        ]
        
        # Test the GameStateManager inventory loading
        play_screen.game_state_manager._load_inventory_from_game_state(sample_inventory_data)
        print("✓ GameStateManager inventory loading successful")
        
        # Verify items were loaded
        loaded_items = 0
        for item in play_screen.hud.inventory.inventory_items:
            if item is not None:
                loaded_items += 1
                print(f"  - Loaded item: {item.get('name')} x{item.get('count')}")
        
        if loaded_items == 3:
            print("✓ All items loaded correctly")
        else:
            print(f"✗ Expected 3 items, got {loaded_items}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Inventory loading test failed: {e}")
        return False

def test_item_collection():
    """Test that item collection works properly"""
    print("\nTesting item collection...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Test adding items to HUD inventory
        success1 = play_screen.inventory_manager._add_item_to_hud_inventory("Key", 1)
        success2 = play_screen.inventory_manager._add_item_to_hud_inventory("Crystal", 2)
        success3 = play_screen.inventory_manager._add_item_to_hud_inventory("Key", 1)  # Should stack
        
        if success1 and success2 and success3:
            print("✓ Item addition successful")
        else:
            print(f"✗ Item addition failed: {success1}, {success2}, {success3}")
            return False
        
        # Verify items were added correctly
        key_count = 0
        crystal_count = 0
        
        for item in play_screen.hud.inventory.inventory_items:
            if item is not None:
                if item.get('name') == 'Key':
                    key_count = item.get('count', 0)
                elif item.get('name') == 'Crystal':
                    crystal_count = item.get('count', 0)
        
        if key_count == 2 and crystal_count == 2:
            print(f"✓ Items stacked correctly: Key x{key_count}, Crystal x{crystal_count}")
        else:
            print(f"✗ Item stacking failed: Key x{key_count}, Crystal x{crystal_count}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Item collection test failed: {e}")
        return False

def test_inventory_transfer():
    """Test that inventory transfer works properly"""
    print("\nTesting inventory transfer...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Test transferring items to HUD inventory
        test_item = {"name": "TestItem", "count": 3}
        success = play_screen.inventory_manager.transfer_item_to_hud_inventory(test_item)
        
        if success:
            print("✓ Item transfer successful")
        else:
            print("✗ Item transfer failed")
            return False
        
        # Verify item was transferred
        found_item = False
        for item in play_screen.hud.inventory.inventory_items:
            if item is not None and item.get('name') == 'TestItem':
                found_item = True
                if item.get('count') == 3:
                    print("✓ Item transferred with correct count")
                else:
                    print(f"✗ Item transferred with wrong count: {item.get('count')}")
                    return False
                break
        
        if not found_item:
            print("✗ Item not found after transfer")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Inventory transfer test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Inventory Fix")
    print("=" * 40)
    
    # Test 1: Inventory loading
    loading_ok = test_inventory_loading()
    
    # Test 2: Item collection
    collection_ok = test_item_collection()
    
    # Test 3: Inventory transfer
    transfer_ok = test_inventory_transfer()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Test Results Summary:")
    print(f"Inventory Loading: {'✓ PASS' if loading_ok else '✗ FAIL'}")
    print(f"Item Collection: {'✓ PASS' if collection_ok else '✗ FAIL'}")
    print(f"Inventory Transfer: {'✓ PASS' if transfer_ok else '✗ FAIL'}")
    
    all_tests_passed = loading_ok and collection_ok and transfer_ok
    
    if all_tests_passed:
        print("\n🎉 All inventory tests passed! The fix is working correctly.")
        return 0
    else:
        print("\n❌ Some inventory tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
