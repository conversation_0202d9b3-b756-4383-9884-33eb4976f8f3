"""
PlayerManager - Manages player character and positioning

This module is responsible for:
- Player character creation and initialization
- Player positioning (saved locations, teleportation, defaults)
- Player health and shield management
- Player boundary setting
- Player location tracking and saving
"""
from character_system import PlayerCharacter


class PlayerManager:
    """Handles player character management and positioning"""
    
    def __init__(self, play_screen):
        """Initialize the PlayerManager with reference to PlayScreen"""
        self.play_screen = play_screen
    
    def initialize_player(self, map_data):
        """Initialize player character with appropriate position"""
        # First check if we're teleporting - if so, we'll set the position later
        if not self.play_screen.is_teleporting:
            self._initialize_player_from_saved_location(map_data)
        else:
            self._initialize_player_for_teleportation(map_data)
        
        # Set map boundaries for the player
        if self.play_screen.player:
            self.play_screen.player.set_map_boundaries(
                0, 0,  # Min X, Min Y
                self.play_screen.map_width * self.play_screen.grid_cell_size,  # Max X
                self.play_screen.map_height * self.play_screen.grid_cell_size  # Max Y
            )
    
    def _initialize_player_from_saved_location(self, map_data):
        """Initialize player from saved location or defaults"""
        # Determine which folder (world) this map belongs to
        folder_name = self.play_screen.player_location_tracker._determine_folder_name(self.play_screen.map_name)
        
        # Get the location for this specific world
        world_location = self.play_screen.player_location_tracker.get_world_location(folder_name)
        
        # Check if we have a saved location for this world
        if world_location:
            self._create_player_from_world_location(world_location, folder_name, map_data)
        # Check if there's a saved location for this specific map (cross-world compatibility)
        elif self.play_screen.player_location_tracker.has_location(self.play_screen.map_name):
            self._create_player_from_map_location(map_data)
        # Check if map has a defined player start position
        elif "player_start" in map_data:
            self._create_player_from_map_start(map_data)
        else:
            self._create_player_at_default_position()
    
    def _create_player_from_world_location(self, world_location, folder_name, map_data):
        """Create player from world-specific saved location"""
        player_x = world_location["x"]
        player_y = world_location["y"]
        player_direction = world_location["direction"]
        
        # Create the player character with saved position and direction
        self.play_screen.player = PlayerCharacter(player_x, player_y)
        self.play_screen.player.direction = player_direction
        
        print(f"DEBUG: Loading map '{self.play_screen.map_name}' in world '{folder_name}'")
        print(f"DEBUG: Saved location was for map '{world_location.get('map_name')}' at ({player_x}, {player_y})")
        print(f"DEBUG: Using saved position for world '{folder_name}': ({player_x}, {player_y})")
        
        # Set health and shield from the world location
        self.play_screen.player.current_health = world_location.get("health", 100)
        self.play_screen.player.shield_durability = world_location.get("shield_durability", 3)
        
        # Set camera position from game state if available
        if "game_state" in map_data and "camera" in map_data["game_state"]:
            self.play_screen.camera_x = map_data["game_state"]["camera"]["x"]
            self.play_screen.camera_y = map_data["game_state"]["camera"]["y"]
    
    def _create_player_from_map_location(self, map_data):
        """Create player from map-specific saved location (fallback)"""
        saved_location = self.play_screen.player_location_tracker.get_location(self.play_screen.map_name)
        if saved_location:
            player_x = saved_location["x"]
            player_y = saved_location["y"]
            player_direction = saved_location["direction"]
            
            # Create the player character with saved position and direction
            self.play_screen.player = PlayerCharacter(player_x, player_y)
            self.play_screen.player.direction = player_direction
            print(f"Loaded saved position for map '{self.play_screen.map_name}' from any world (fallback): ({player_x}, {player_y})")
            
            # Set default health and shield (since cross-world might not have these)
            self.play_screen.player.current_health = 100
            self.play_screen.player.shield_durability = 3
    
    def _create_player_from_map_start(self, map_data):
        """Create player from map's defined start position"""
        player_grid_x = map_data["player_start"].get("x", 0)
        player_grid_y = map_data["player_start"].get("y", 0)
        player_x = player_grid_x * self.play_screen.grid_cell_size
        player_y = player_grid_y * self.play_screen.grid_cell_size
        player_direction = map_data["player_start"].get("direction", "down")
        
        # Create the player character with starting position
        self.play_screen.player = PlayerCharacter(player_x, player_y)
        self.play_screen.player.direction = player_direction
        print(f"Using map's player_start position: ({player_x}, {player_y})")
    
    def _create_player_at_default_position(self):
        """Create player at default center position"""
        player_x = (self.play_screen.map_width * self.play_screen.grid_cell_size) // 2
        player_y = (self.play_screen.map_height * self.play_screen.grid_cell_size) // 2
        
        # Create the player character (default direction is already "down")
        self.play_screen.player = PlayerCharacter(player_x, player_y)
        print(f"Using default center position: ({player_x}, {player_y})")
    
    def _initialize_player_for_teleportation(self, map_data):
        """Initialize player for teleportation (position will be set later)"""
        # We're teleporting, so we'll create a default player that will be positioned later
        player_x = 0
        player_y = 0
        self.play_screen.player = PlayerCharacter(player_x, player_y)
        
        # Set health and shield from game state if available
        if "game_state" in map_data and "player" in map_data["game_state"]:
            player_data = map_data["game_state"]["player"]
            if "health" in player_data:
                self.play_screen.player.current_health = player_data["health"]
            if "shield_durability" in player_data:
                self.play_screen.player.shield_durability = player_data["shield_durability"]
    
    def save_player_location(self):
        """Save current player location"""
        if self.play_screen.player and self.play_screen.map_name:
            # Determine which folder (world) the current map belongs to
            current_folder_name = self.play_screen.player_location_tracker._determine_folder_name(self.play_screen.map_name)
            
            print(f"DEBUG: Saving player location for world {current_folder_name}, map {self.play_screen.map_name}")
            
            # Save the current player position for the current map and world
            self.play_screen.player_location_tracker.save_location(
                self.play_screen.map_name,
                self.play_screen.player.rect.x,
                self.play_screen.player.rect.y,
                self.play_screen.player.direction,
                self.play_screen.player.current_health,
                self.play_screen.player.shield_durability,
                current_folder_name
            )
            # Save to file
            self.play_screen.player_location_tracker.save_to_file()
    
    def get_player(self):
        """Get the current player character"""
        return self.play_screen.player
    
    def is_player_alive(self):
        """Check if player is alive"""
        return self.play_screen.player and not self.play_screen.player.is_dead
    
    def is_player_death_animation_complete(self):
        """Check if player death animation is complete"""
        return (self.play_screen.player and 
                self.play_screen.player.is_dead and 
                self.play_screen.player.death_animation_complete)
    
    def update_player(self):
        """Update player character"""
        if self.play_screen.player:
            # Store original position for collision detection
            original_x = self.play_screen.player.rect.x
            original_y = self.play_screen.player.rect.y
            
            # Update player (handles input and animation)
            self.play_screen.player.update()
            
            return original_x, original_y
        return None, None
    
    def handle_player_collision(self, original_x, original_y):
        """Handle player collision with map boundaries and obstacles"""
        if not self.play_screen.player:
            return
            
        # Check for collisions with map tiles
        if hasattr(self.play_screen, 'layers') and self.play_screen.layers:
            # Check collision with the first layer (ground layer)
            if self.play_screen.collision_handler.check_collision(
                self.play_screen.player.rect,
                self.play_screen.expanded_mapping,
                self.play_screen.layers[0]["data"]
            ):
                # Collision detected, revert to original position
                self.play_screen.player.rect.x = original_x
                self.play_screen.player.rect.y = original_y
