#!/usr/bin/env python3
"""
Test script to verify the collision improvements are working correctly.

This script tests:
1. Boundary collision detection (player can't walk outside map)
2. Multi-layer collision detection (all layers checked, not just first)
3. Lootchest collision integration
4. All collision types working together
"""

import sys
import os

# Add the game_core directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game_core'))

def test_boundary_collision():
    """Test that boundary collision detection works"""
    print("Testing boundary collision detection...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success and play_screen.player:
            # Test boundary collision detection method
            original_x, original_y = play_screen.player.rect.x, play_screen.player.rect.y
            
            # Move player to boundary positions and test collision detection
            # Test left boundary
            play_screen.player.rect.x = -10  # Outside left boundary
            boundary_collision = play_screen.collision_manager._check_boundary_collision()
            if boundary_collision:
                print("✓ Left boundary collision detected correctly")
            else:
                print("✗ Left boundary collision not detected")
                return False
            
            # Test right boundary
            map_width_pixels = play_screen.map_width * play_screen.grid_cell_size
            play_screen.player.rect.x = map_width_pixels + 10  # Outside right boundary
            boundary_collision = play_screen.collision_manager._check_boundary_collision()
            if boundary_collision:
                print("✓ Right boundary collision detected correctly")
            else:
                print("✗ Right boundary collision not detected")
                return False
            
            # Test top boundary
            play_screen.player.rect.x = original_x  # Reset x
            play_screen.player.rect.y = -10  # Outside top boundary
            boundary_collision = play_screen.collision_manager._check_boundary_collision()
            if boundary_collision:
                print("✓ Top boundary collision detected correctly")
            else:
                print("✗ Top boundary collision not detected")
                return False
            
            # Test bottom boundary
            map_height_pixels = play_screen.map_height * play_screen.grid_cell_size
            play_screen.player.rect.y = map_height_pixels + 10  # Outside bottom boundary
            boundary_collision = play_screen.collision_manager._check_boundary_collision()
            if boundary_collision:
                print("✓ Bottom boundary collision detected correctly")
            else:
                print("✗ Bottom boundary collision not detected")
                return False
            
            # Test inside boundaries (should not collide)
            play_screen.player.rect.x = original_x
            play_screen.player.rect.y = original_y
            boundary_collision = play_screen.collision_manager._check_boundary_collision()
            if not boundary_collision:
                print("✓ Inside boundaries - no collision detected correctly")
            else:
                print("✗ Inside boundaries - false collision detected")
                return False
            
            return True
        else:
            print("⚠ No player to test boundary collision with")
            return True
        
    except Exception as e:
        print(f"✗ Boundary collision test failed: {e}")
        return False

def test_multi_layer_collision():
    """Test that multi-layer collision detection works"""
    print("\nTesting multi-layer collision detection...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success and play_screen.player and hasattr(play_screen, 'layers'):
            layer_count = len(play_screen.layers)
            print(f"✓ Map has {layer_count} layers")
            
            # Test that collision manager checks all layers
            original_x, original_y = play_screen.player.rect.x, play_screen.player.rect.y
            
            # The collision manager should check all visible layers
            visible_layers = [layer for layer in play_screen.layers if layer.get("visible", True)]
            print(f"✓ Found {len(visible_layers)} visible layers to check")
            
            # Test collision checking (this will internally check all layers)
            play_screen.collision_manager.check_player_collisions(original_x, original_y)
            print("✓ Multi-layer collision checking completed without errors")
            
            return True
        else:
            print("⚠ No layers or player to test multi-layer collision with")
            return True
        
    except Exception as e:
        print(f"✗ Multi-layer collision test failed: {e}")
        return False

def test_lootchest_integration():
    """Test that lootchest collision integration works"""
    print("\nTesting lootchest collision integration...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success and play_screen.player:
            # Test lootchest collision checking
            play_screen.collision_manager.check_lootchest_collisions()
            print("✓ Lootchest collision checking completed without errors")
            
            # Test that lootchest manager is accessible
            if hasattr(play_screen, 'lootchest_manager'):
                print("✓ Lootchest manager is accessible")
            else:
                print("✗ Lootchest manager not accessible")
                return False
            
            # Test hover detection method exists
            if hasattr(play_screen, 'is_hovering_lootchest'):
                print("✓ Lootchest hover detection method exists")
            else:
                print("✗ Lootchest hover detection method missing")
                return False
            
            return True
        else:
            print("⚠ No player to test lootchest integration with")
            return True
        
    except Exception as e:
        print(f"✗ Lootchest integration test failed: {e}")
        return False

def test_collision_integration():
    """Test that all collision types work together"""
    print("\nTesting collision system integration...")
    
    try:
        import pygame
        pygame.init()
        
        # Set up a minimal display for pygame
        pygame.display.set_mode((800, 600), pygame.HIDDEN)
        
        from gameplay.play_screen import PlayScreen
        
        # Create a PlayScreen instance
        play_screen = PlayScreen(800, 600)
        
        # Load a map
        success = play_screen.load_map("main")
        
        if success and play_screen.player:
            original_x, original_y = play_screen.player.rect.x, play_screen.player.rect.y
            
            # Test full collision pipeline
            play_screen.collision_manager.check_player_collisions(original_x, original_y)
            play_screen.collision_manager.check_enemy_collisions()
            play_screen.collision_manager.check_item_collisions()
            play_screen.collision_manager.check_lootchest_collisions()
            relation = play_screen.collision_manager.check_relation_point_collisions()
            
            print("✓ All collision types checked successfully")
            print(f"✓ Relation point collision result: {relation}")
            
            # Test that player position is maintained correctly
            if (play_screen.player.rect.x == original_x and 
                play_screen.player.rect.y == original_y):
                print("✓ Player position maintained correctly after collision checks")
            else:
                print(f"⚠ Player position changed: ({original_x}, {original_y}) -> ({play_screen.player.rect.x}, {play_screen.player.rect.y})")
            
            return True
        else:
            print("⚠ No player to test collision integration with")
            return True
        
    except Exception as e:
        print(f"✗ Collision integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Collision System Improvements")
    print("=" * 50)
    
    # Test 1: Boundary collision
    boundary_ok = test_boundary_collision()
    
    # Test 2: Multi-layer collision
    multi_layer_ok = test_multi_layer_collision()
    
    # Test 3: Lootchest integration
    lootchest_ok = test_lootchest_integration()
    
    # Test 4: Collision integration
    integration_ok = test_collision_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"Boundary Collision: {'✓ PASS' if boundary_ok else '✗ FAIL'}")
    print(f"Multi-Layer Collision: {'✓ PASS' if multi_layer_ok else '✗ FAIL'}")
    print(f"Lootchest Integration: {'✓ PASS' if lootchest_ok else '✗ FAIL'}")
    print(f"Collision Integration: {'✓ PASS' if integration_ok else '✗ FAIL'}")
    
    all_tests_passed = boundary_ok and multi_layer_ok and lootchest_ok and integration_ok
    
    if all_tests_passed:
        print("\n🎉 All collision improvement tests passed! Enhanced collision system is working correctly.")
        return 0
    else:
        print("\n❌ Some collision improvement tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
